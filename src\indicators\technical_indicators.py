"""
Technical Indicators for Gold Trading
Implements MACD, ATR, and Pivot Points calculations
"""

import pandas as pd
import numpy as np
from typing import Tuple, Dict, Optional
from dataclasses import dataclass

from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class MACDSignal:
    """MACD signal data structure"""
    macd: float
    signal: float
    histogram: float
    trend: str  # 'bullish', 'bearish', 'neutral'
    crossover: str  # 'bullish_cross', 'bearish_cross', 'none'


@dataclass
class ATRData:
    """ATR data structure"""
    atr: float
    atr_percentage: float
    volatility_level: str  # 'low', 'medium', 'high'


@dataclass
class PivotPoints:
    """Pivot points data structure"""
    pivot: float
    r1: float
    r2: float
    r3: float
    s1: float
    s2: float
    s3: float
    current_level: str  # 'above_pivot', 'below_pivot', 'at_pivot'


class TechnicalIndicators:
    """Technical indicators calculator for gold trading"""

    def __init__(self, config=None):
        # Use config values if provided, otherwise use defaults
        if config and hasattr(config, 'indicators'):
            self.macd_fast = config.indicators.macd_fast
            self.macd_slow = config.indicators.macd_slow
            self.macd_signal = config.indicators.macd_signal
            self.atr_period = config.indicators.atr_period
        else:
            # Default values
            self.macd_fast = 12
            self.macd_slow = 26
            self.macd_signal = 9
            self.atr_period = 14

    def calculate_macd(self, data: pd.DataFrame,
                      fast_period: Optional[int] = None,
                      slow_period: Optional[int] = None,
                      signal_period: Optional[int] = None) -> MACDSignal:
        """
        Calculate MACD indicator

        Args:
            data: DataFrame with OHLC data
            fast_period: Fast EMA period (default: 12)
            slow_period: Slow EMA period (default: 26)
            signal_period: Signal line EMA period (default: 9)

        Returns:
            MACDSignal object with current MACD values and signals
        """
        if data is None or len(data) < 50:
            logger.warning("Insufficient data for MACD calculation")
            return MACDSignal(0, 0, 0, 'neutral', 'none')

        fast = fast_period or self.macd_fast
        slow = slow_period or self.macd_slow
        signal = signal_period or self.macd_signal

        try:
            # Calculate EMA
            ema_fast = data['close'].ewm(span=fast).mean()
            ema_slow = data['close'].ewm(span=slow).mean()

            # Calculate MACD line
            macd_line = ema_fast - ema_slow

            # Calculate signal line
            signal_line = macd_line.ewm(span=signal).mean()

            # Calculate histogram
            histogram = macd_line - signal_line

            # Get current values
            current_macd = float(macd_line.iloc[-1]) if len(macd_line) > 0 else 0
            current_signal = float(signal_line.iloc[-1]) if len(signal_line) > 0 else 0
            current_histogram = float(histogram.iloc[-1]) if len(histogram) > 0 else 0

            # Handle NaN values
            current_macd = 0 if pd.isna(current_macd) else current_macd
            current_signal = 0 if pd.isna(current_signal) else current_signal
            current_histogram = 0 if pd.isna(current_histogram) else current_histogram

            # Determine trend
            trend = self._determine_macd_trend(current_macd, current_signal, current_histogram)

            # Detect crossovers
            crossover = self._detect_macd_crossover(macd_line, signal_line)

            return MACDSignal(
                macd=current_macd,
                signal=current_signal,
                histogram=current_histogram,
                trend=trend,
                crossover=crossover
            )

        except Exception as e:
            logger.error(f"Error calculating MACD: {e}")
            return MACDSignal(0, 0, 0, 'neutral', 'none')

    def calculate_atr(self, data: pd.DataFrame, period: Optional[int] = None) -> ATRData:
        """
        Calculate Average True Range (ATR)

        Args:
            data: DataFrame with OHLC data
            period: ATR calculation period (default: 14)

        Returns:
            ATRData object with ATR values and volatility assessment
        """
        if data is None or len(data) < 20:
            logger.warning("Insufficient data for ATR calculation")
            return ATRData(0, 0, 'medium')

        period = period or self.atr_period

        try:
            # Calculate True Range
            high_low = data['high'] - data['low']
            high_close = np.abs(data['high'] - data['close'].shift())
            low_close = np.abs(data['low'] - data['close'].shift())

            true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)

            # Calculate ATR using EMA
            atr_values = true_range.ewm(span=period).mean()

            current_atr = float(atr_values.iloc[-1]) if len(atr_values) > 0 and not pd.isna(atr_values.iloc[-1]) else 0
            current_price = float(data['close'].iloc[-1]) if len(data) > 0 else 0

            # Calculate ATR as percentage of current price
            atr_percentage = (current_atr / current_price) * 100 if current_price > 0 else 0

            # Determine volatility level
            volatility_level = self._assess_volatility(atr_percentage)

            return ATRData(
                atr=current_atr,
                atr_percentage=atr_percentage,
                volatility_level=volatility_level
            )

        except Exception as e:
            logger.error(f"Error calculating ATR: {e}")
            return ATRData(0, 0, 'medium')

    def calculate_pivot_points(self, data: pd.DataFrame) -> PivotPoints:
        """
        Calculate Pivot Points (Standard method)

        Args:
            data: DataFrame with OHLC data

        Returns:
            PivotPoints object with all pivot levels
        """
        if data is None or len(data) < 2:
            logger.warning("Insufficient data for Pivot Points calculation")
            return PivotPoints(0, 0, 0, 0, 0, 0, 0, 'at_pivot')

        try:
            # Use previous day's data for pivot calculation
            prev_high = float(data['high'].iloc[-2]) if len(data) > 1 else 0
            prev_low = float(data['low'].iloc[-2]) if len(data) > 1 else 0
            prev_close = float(data['close'].iloc[-2]) if len(data) > 1 else 0
            current_price = float(data['close'].iloc[-1]) if len(data) > 0 else 0

            # Calculate pivot point
            pivot = (prev_high + prev_low + prev_close) / 3

            # Calculate resistance levels
            r1 = 2 * pivot - prev_low
            r2 = pivot + (prev_high - prev_low)
            r3 = prev_high + 2 * (pivot - prev_low)

            # Calculate support levels
            s1 = 2 * pivot - prev_high
            s2 = pivot - (prev_high - prev_low)
            s3 = prev_low - 2 * (prev_high - pivot)

            # Determine current level relative to pivot
            current_level = self._determine_pivot_level(current_price, pivot)

            return PivotPoints(
                pivot=pivot,
                r1=r1, r2=r2, r3=r3,
                s1=s1, s2=s2, s3=s3,
                current_level=current_level
            )

        except Exception as e:
            logger.error(f"Error calculating Pivot Points: {e}")
            return PivotPoints(0, 0, 0, 0, 0, 0, 0, 'at_pivot')

    def get_combined_signal(self, data: pd.DataFrame) -> Dict:
        """
        Get combined signal from all indicators

        Args:
            data: DataFrame with OHLC data

        Returns:
            Dictionary with all indicator signals and overall assessment
        """
        macd_signal = self.calculate_macd(data)
        atr_data = self.calculate_atr(data)
        pivot_points = self.calculate_pivot_points(data)

        # Calculate overall signal strength
        signal_strength = self._calculate_signal_strength(macd_signal, pivot_points)

        # Determine trading recommendation
        recommendation = self._get_trading_recommendation(
            macd_signal, atr_data, pivot_points, signal_strength
        )

        return {
            'macd': {
                'value': macd_signal.macd,
                'signal': macd_signal.signal,
                'histogram': macd_signal.histogram,
                'trend': macd_signal.trend,
                'crossover': macd_signal.crossover
            },
            'atr': {
                'value': atr_data.atr,
                'percentage': atr_data.atr_percentage,
                'volatility': atr_data.volatility_level
            },
            'pivot_points': {
                'pivot': pivot_points.pivot,
                'resistance': [pivot_points.r1, pivot_points.r2, pivot_points.r3],
                'support': [pivot_points.s1, pivot_points.s2, pivot_points.s3],
                'current_level': pivot_points.current_level
            },
            'signal_strength': signal_strength,
            'recommendation': recommendation,
            'timestamp': pd.Timestamp.now()
        }

    def _determine_macd_trend(self, macd: float, signal: float, histogram: float) -> str:
        """Determine MACD trend direction"""
        if macd > signal and histogram > 0:
            return 'bullish'
        elif macd < signal and histogram < 0:
            return 'bearish'
        else:
            return 'neutral'

    def _detect_macd_crossover(self, macd_line: pd.Series, signal_line: pd.Series) -> str:
        """Detect MACD crossover signals"""
        # Check if inputs are pandas Series
        if not isinstance(macd_line, pd.Series) or not isinstance(signal_line, pd.Series):
            return 'none'

        if len(macd_line) < 2 or len(signal_line) < 2:
            return 'none'

        # Check for bullish crossover (MACD crosses above signal)
        if (macd_line.iloc[-2] <= signal_line.iloc[-2] and
            macd_line.iloc[-1] > signal_line.iloc[-1]):
            return 'bullish_cross'

        # Check for bearish crossover (MACD crosses below signal)
        elif (macd_line.iloc[-2] >= signal_line.iloc[-2] and
              macd_line.iloc[-1] < signal_line.iloc[-1]):
            return 'bearish_cross'

        return 'none'

    def _assess_volatility(self, atr_percentage: float) -> str:
        """Assess volatility level based on ATR percentage"""
        if atr_percentage < 0.5:
            return 'low'
        elif atr_percentage < 1.0:
            return 'medium'
        else:
            return 'high'

    def _determine_pivot_level(self, current_price: float, pivot: float) -> str:
        """Determine current price level relative to pivot"""
        if current_price > pivot:
            return 'above_pivot'
        elif current_price < pivot:
            return 'below_pivot'
        else:
            return 'at_pivot'

    def _calculate_signal_strength(self, macd_signal: MACDSignal,
                                 pivot_points: PivotPoints) -> float:
        """Calculate overall signal strength"""
        strength = 0.0

        # MACD contribution
        if macd_signal.crossover == 'bullish_cross':
            strength += 0.4
        elif macd_signal.crossover == 'bearish_cross':
            strength -= 0.4
        elif macd_signal.trend == 'bullish':
            strength += 0.2
        elif macd_signal.trend == 'bearish':
            strength -= 0.2

        # Pivot points contribution
        if pivot_points.current_level == 'above_pivot':
            strength += 0.2
        elif pivot_points.current_level == 'below_pivot':
            strength -= 0.2

        return abs(strength)

    def _get_trading_recommendation(self, macd_signal: MACDSignal,
                                  atr_data: ATRData,
                                  pivot_points: PivotPoints,
                                  signal_strength: float) -> str:
        """Get trading recommendation based on all indicators"""
        if signal_strength < 0.3:
            return 'hold'

        if macd_signal.trend == 'bullish' and pivot_points.current_level == 'above_pivot':
            return 'buy'
        elif macd_signal.trend == 'bearish' and pivot_points.current_level == 'below_pivot':
            return 'sell'

        return 'hold'
